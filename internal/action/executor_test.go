package action

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/mubeng/mubeng/internal/interfaces"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockLogService 模拟日志服务
type MockLogService struct {
	mock.Mock
}

func (m *MockLogService) Debug(format string, args ...interface{}) {
	m.Called(format, args)
}

func (m *MockLogService) Info(format string, args ...interface{}) {
	m.Called(format, args)
}

func (m *MockLogService) Warn(format string, args ...interface{}) {
	m.Called(format, args)
}

func (m *MockLogService) Error(format string, args ...interface{}) {
	m.Called(format, args)
}

func (m *MockLogService) Fatal(format string, args ...interface{}) {
	m.Called(format, args)
}

func (m *MockLogService) WithTraceID(traceID string) interfaces.LogService {
	return m
}

func (m *MockLogService) WithFields(fields map[string]interface{}) interfaces.LogService {
	return m
}

func (m *MockLogService) LogError(err error, msg string, args ...interface{}) {
	m.Called(err, msg, args)
}

func (m *MockLogService) GetLogger() interface{} {
	return m
}

// MockProxyService 模拟代理服务
type MockProxyService struct {
	mock.Mock
}

func (m *MockProxyService) BanIP(ip string, duration int) error {
	args := m.Called(ip, duration)
	return args.Error(0)
}

func (m *MockProxyService) BanDomain(domain string, duration int) error {
	args := m.Called(domain, duration)
	return args.Error(0)
}

func (m *MockProxyService) GetProxy() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockProxyService) RemoveProxy(proxy string) error {
	args := m.Called(proxy)
	return args.Error(0)
}

func (m *MockProxyService) AddProxy(proxy string) error {
	args := m.Called(proxy)
	return args.Error(0)
}

func (m *MockProxyService) GetProxyCount() int {
	args := m.Called()
	return args.Int(0)
}

func (m *MockProxyService) IsHealthy() bool {
	args := m.Called()
	return args.Bool(0)
}

func (m *MockProxyService) GetStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

// 实现ProxyService接口的其他方法
func (m *MockProxyService) GetNextProxy() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockProxyService) MarkProxyFailed(proxy string) {
	m.Called(proxy)
}

func (m *MockProxyService) MarkProxySuccess(proxy string) {
	m.Called(proxy)
}

func (m *MockProxyService) InitBanSystem(config interface{}) {
	m.Called(config)
}

func (m *MockProxyService) StartBanCleaner(ctx context.Context) {
	m.Called(ctx)
}

func (m *MockProxyService) IsIPBanned(ip string) bool {
	args := m.Called(ip)
	return args.Bool(0)
}

func (m *MockProxyService) IsDomainBanned(domain string) bool {
	args := m.Called(domain)
	return args.Bool(0)
}

func (m *MockProxyService) UnbanIP(ip string) error {
	args := m.Called(ip)
	return args.Error(0)
}

func (m *MockProxyService) UnbanDomain(domain string) error {
	args := m.Called(domain)
	return args.Error(0)
}

func (m *MockProxyService) UpdateProxyList(proxies []string) {
	m.Called(proxies)
}

func (m *MockProxyService) GetBanStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockProxyService) GetProxyStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockProxyService) ResetFailedProxies() {
	m.Called()
}

func (m *MockProxyService) Stop() {
	m.Called()
}

// MockCacheService 模拟缓存服务
type MockCacheService struct {
	mock.Mock
}

func (m *MockCacheService) Set(key string, value interface{}, ttl time.Duration) error {
	args := m.Called(key, value, ttl)
	return args.Error(0)
}

func (m *MockCacheService) Get(key string) (interface{}, bool) {
	args := m.Called(key)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheService) Delete(key string) error {
	args := m.Called(key)
	return args.Error(0)
}

func (m *MockCacheService) Clear() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockCacheService) GetStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

// 实现CacheService接口的其他方法
func (m *MockCacheService) GetDNSCache(key string) ([]string, bool) {
	args := m.Called(key)
	return args.Get(0).([]string), args.Bool(1)
}

func (m *MockCacheService) SetDNSCache(key string, value []string, ttl int) {
	m.Called(key, value, ttl)
}

func (m *MockCacheService) GetRegexCache(pattern string) (interface{}, bool) {
	args := m.Called(pattern)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheService) SetRegexCache(pattern string, value interface{}) {
	m.Called(pattern, value)
}

func (m *MockCacheService) UpdateProxyPool(proxies []string) {
	m.Called(proxies)
}

func (m *MockCacheService) StartCleanupRoutine() {
	m.Called()
}

func (m *MockCacheService) GetCacheStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockCacheService) ClearAllCache() {
	m.Called()
}

// TestLogExecutor 测试日志执行器
func TestLogExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &LogExecutor{Logger: mockLogger}

	t.Run("Execute with valid message", func(t *testing.T) {
		mockLogger.On("Info", "Test message").Return()
		
		params := map[string]interface{}{
			"message": "Test message",
			"level":   "info",
		}
		
		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with missing message", func(t *testing.T) {
		params := map[string]interface{}{
			"level": "info",
		}
		
		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少message参数")
	})

	t.Run("Validate with valid parameters", func(t *testing.T) {
		params := map[string]interface{}{
			"message": "Test message",
		}
		
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate with missing message", func(t *testing.T) {
		params := map[string]interface{}{}
		
		err := executor.Validate(params)
		assert.Error(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "log", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "记录日志信息", executor.GetDescription())
	})
}

// TestBanIPExecutor 测试IP封禁执行器
func TestBanIPExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &BanIPExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with IP parameter", func(t *testing.T) {
		mockProxy.On("BanIP", "***********00", 3600).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"ip": "***********00",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with custom duration", func(t *testing.T) {
		mockProxy.On("BanIP", "********", 7200).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"ip":       "********",
			"duration": 7200,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute without IP parameter", func(t *testing.T) {
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无法获取要封禁的IP地址")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate", func(t *testing.T) {
		params := map[string]interface{}{}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "banip", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "封禁IP地址", executor.GetDescription())
	})
}

// TestBanDomainExecutor 测试域名封禁执行器
func TestBanDomainExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &BanDomainExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with domain parameter", func(t *testing.T) {
		mockProxy.On("BanDomain", "example.com", 3600).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"domain": "example.com",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with permanent ban", func(t *testing.T) {
		mockProxy.On("BanDomain", "malicious.com", 31536000).Return(nil) // 1年
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"domain":    "malicious.com",
			"permanent": true,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute without domain parameter", func(t *testing.T) {
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无法获取要封禁的域名")
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "ban_domain", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "封禁域名", executor.GetDescription())
	})
}

// TestBlockRequestExecutor 测试阻止请求执行器
func TestBlockRequestExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &BlockRequestExecutor{Logger: mockLogger}

	t.Run("ExecuteHTTP with default parameters", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.RemoteAddr = "***********:12345"

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}

		modifiedReq, resp, err := executor.ExecuteHTTP(context.Background(), params, req, nil)
		assert.NoError(t, err)
		assert.Equal(t, req, modifiedReq)
		assert.NotNil(t, resp)
		assert.Equal(t, 403, resp.StatusCode)
		assert.Equal(t, "1", resp.Header.Get("X-FlexProxy-Blocked"))
		mockLogger.AssertExpectations(t)
	})

	t.Run("ExecuteHTTP with custom parameters", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "http://api.example.com", nil)
		req.RemoteAddr = "********:54321"

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"reason":      "安全策略阻止",
			"status_code": 429,
			"body":        `{"error": "Rate limited"}`,
		}

		modifiedReq, resp, err := executor.ExecuteHTTP(context.Background(), params, req, nil)
		assert.NoError(t, err)
		assert.Equal(t, req, modifiedReq)
		assert.NotNil(t, resp)
		assert.Equal(t, 429, resp.StatusCode)
		assert.Equal(t, "安全策略阻止", resp.Header.Get("X-FlexProxy-Reason"))
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "block_request", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "阻止请求", executor.GetDescription())
	})
}

// TestRetrySameExecutor 测试相同IP重试执行器
func TestRetrySameExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &RetrySameExecutor{Logger: mockLogger}

	t.Run("Execute with default retry count", func(t *testing.T) {
		mockLogger.On("Info", "标记使用相同IP重试 %d 次, 延迟: %s", 1, "1s").Return()
		
		params := map[string]interface{}{}
		
		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom retry count", func(t *testing.T) {
		mockLogger.On("Info", "标记使用相同IP重试 %d 次, 延迟: %s", 3, "2s").Return()
		
		params := map[string]interface{}{
			"retry_count": 3,
			"delay":       "2s",
		}
		
		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with attempts parameter", func(t *testing.T) {
		mockLogger.On("Info", "标记使用相同IP重试 %d 次, 延迟: %s", 5, "1s").Return()
		
		params := map[string]interface{}{
			"attempts": 5,
		}
		
		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate", func(t *testing.T) {
		params := map[string]interface{}{}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "retry_same", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "使用相同IP重试请求", executor.GetDescription())
	})
}

// TestRetryExecutor 测试新IP重试执行器
func TestRetryExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &RetryExecutor{Logger: mockLogger}

	t.Run("Execute with default retry count", func(t *testing.T) {
		mockLogger.On("Info", "标记使用新IP重试 %d 次, 延迟: %s", 1, "2s").Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom retry count", func(t *testing.T) {
		mockLogger.On("Info", "标记使用新IP重试 %d 次, 延迟: %s", 3, "3s").Return()

		params := map[string]interface{}{
			"retry_count": 3,
			"delay":       "3s",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "retry", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "使用新IP重试请求", executor.GetDescription())
	})
}

// TestRequestURLExecutor 测试请求URL执行器
func TestRequestURLExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &RequestURLExecutor{Logger: mockLogger}

	t.Run("Execute with valid URL", func(t *testing.T) {
		mockLogger.On("Info", "请求URL动作已执行: %s %s (timeout: %dms, follow_redirect: %v)",
			"GET", "https://example.com", 30000, true).Return()

		params := map[string]interface{}{
			"url": "https://example.com",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom method and timeout", func(t *testing.T) {
		mockLogger.On("Info", "请求URL动作已执行: %s %s (timeout: %dms, follow_redirect: %v)",
			"POST", "https://api.example.com", 5000, false).Return()

		params := map[string]interface{}{
			"url":             "https://api.example.com",
			"method":          "post",
			"timeout_ms":      5000,
			"follow_redirect": false,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with missing URL", func(t *testing.T) {
		params := map[string]interface{}{
			"method": "GET",
		}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少url参数")
	})

	t.Run("Validate with valid URL", func(t *testing.T) {
		params := map[string]interface{}{
			"url": "https://example.com",
		}

		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate with missing URL", func(t *testing.T) {
		params := map[string]interface{}{}

		err := executor.Validate(params)
		assert.Error(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "request_url", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "向指定URL发送请求", executor.GetDescription())
	})
}

// TestCacheExecutor 测试缓存执行器
func TestCacheExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockCache := new(MockCacheService)
	executor := &CacheExecutor{
		Logger:       mockLogger,
		CacheService: mockCache,
	}

	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger.On("Info", "缓存动作已执行: duration=%d, max_use_count=%d, cache_scope=%s, ignore_params=%v",
			300000, 0, "url", false).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger.On("Info", "缓存动作已执行: duration=%d, max_use_count=%d, cache_scope=%s, ignore_params=%v",
			600000, 5, "domain", true).Return()

		params := map[string]interface{}{
			"duration":      600000,
			"max_use_count": 5,
			"cache_scope":   "domain",
			"ignore_params": true,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "cache", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "缓存响应内容", executor.GetDescription())
	})
}

// TestNullResponseExecutor 测试空响应执行器
func TestNullResponseExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &NullResponseExecutor{Logger: mockLogger}

	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger.On("Info", "空响应动作已执行: status_code=%d, content_type=%s, body_length=%d",
			200, "text/plain", 0).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger.On("Info", "空响应动作已执行: status_code=%d, content_type=%s, body_length=%d",
			404, "application/json", 25).Return()

		params := map[string]interface{}{
			"status_code":  404,
			"content_type": "application/json",
			"body":         `{"error": "Not found"}`,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "null_response", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "返回空响应或自定义响应", executor.GetDescription())
	})
}

// TestBypassProxyExecutor 测试绕过代理执行器
func TestBypassProxyExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &BypassProxyExecutor{Logger: mockLogger}

	t.Run("Execute with default timeout", func(t *testing.T) {
		mockLogger.On("Info", "绕过代理动作已执行: timeout=%dms", 30000).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom timeout", func(t *testing.T) {
		mockLogger.On("Info", "绕过代理动作已执行: timeout=%dms", 10000).Return()

		params := map[string]interface{}{
			"timeout_ms": 10000,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with timeout parameter", func(t *testing.T) {
		mockLogger.On("Info", "绕过代理动作已执行: timeout=%dms", 15000).Return()

		params := map[string]interface{}{
			"timeout": 15000,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "bypass_proxy", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "绕过代理直接连接", executor.GetDescription())
	})
}

// TestSaveToPoolExecutor 测试保存到代理池执行器
func TestSaveToPoolExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &SaveToPoolExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger.On("Info", "保存到代理池动作已执行: quality_tier=%s, domain_specific=%v, min_score=%.1f, pool_name=%s",
			"auto", false, 70.0, "default_pool").Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger.On("Info", "保存到代理池动作已执行: quality_tier=%s, domain_specific=%v, min_score=%.1f, pool_name=%s",
			"premium", true, 85.5, "premium_pool").Return()

		params := map[string]interface{}{
			"quality_tier":    "premium",
			"domain_specific": true,
			"min_score":       85.5,
			"pool_name":       "premium_pool",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "save_to_pool", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "保存代理到质量池", executor.GetDescription())
	})
}
